import 'package:dartz/dartz.dart';
import '../entities/learning_progress.dart';
import '../entities/learning_session.dart';
import '../entities/quiz.dart';
import '../../../../core/error/failures.dart';

/// Repository interface for learning progress operations
abstract class LearningProgressRepository {
  /// Saves learning progress to the repository
  Future<Either<Failure, void>> saveLearningProgress(LearningProgress progress);

  /// Retrieves learning progress for a user and subject
  Future<Either<Failure, LearningProgress?>> getLearningProgress({
    required String userId,
    required String subject,
    String? topic,
  });

  /// Retrieves all learning progress for a user
  Future<Either<Failure, List<LearningProgress>>> getUserLearningProgress(
    String userId,
  );

  /// Updates concept progress for a specific concept
  Future<Either<Failure, void>> updateConceptProgress({
    required String userId,
    required String subject,
    required String concept,
    required double progress,
  });

  /// Adds a concept to mastered concepts
  Future<Either<Failure, void>> markConceptAsMastered({
    required String userId,
    required String subject,
    required String concept,
  });

  /// Adds a concept to struggling concepts
  Future<Either<Failure, void>> markConceptAsStruggling({
    required String userId,
    required String subject,
    required String concept,
  });

  /// Removes a concept from struggling concepts
  Future<Either<Failure, void>> removeConceptFromStruggling({
    required String userId,
    required String subject,
    required String concept,
  });

  /// Updates learning statistics
  Future<Either<Failure, void>> updateLearningStats({
    required String userId,
    required String subject,
    required LearningStats stats,
  });

  /// Records a learning session
  Future<Either<Failure, void>> recordLearningSession(LearningSession session);

  /// Gets learning sessions for a user
  Future<Either<Failure, List<LearningSession>>> getLearningSessionsForUser({
    required String userId,
    String? subject,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Gets learning analytics for a user
  Future<Either<Failure, LearningAnalytics>> getLearningAnalytics({
    required String userId,
    String? subject,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Gets learning streaks for a user
  Future<Either<Failure, LearningStreak>> getLearningStreak(String userId);

  /// Updates daily learning activity
  Future<Either<Failure, void>> updateDailyActivity({
    required String userId,
    required DateTime date,
    required int minutesStudied,
  });

  /// Gets weekly learning activity
  Future<Either<Failure, Map<String, int>>> getWeeklyActivity({
    required String userId,
    required DateTime weekStart,
  });

  /// Gets learning goals for a user
  Future<Either<Failure, List<LearningGoal>>> getLearningGoals(String userId);

  /// Saves a learning goal
  Future<Either<Failure, void>> saveLearningGoal(LearningGoal goal);

  /// Updates learning goal progress
  Future<Either<Failure, void>> updateLearningGoalProgress({
    required String goalId,
    required double progress,
  });

  /// Deletes a learning goal
  Future<Either<Failure, void>> deleteLearningGoal(String goalId);

  /// Gets learning recommendations based on progress
  Future<Either<Failure, List<LearningRecommendation>>>
  getLearningRecommendations({required String userId, String? subject});
}

/// Represents learning analytics data
class LearningAnalytics {
  final String userId;
  final DateTime startDate;
  final DateTime endDate;
  final int totalStudyTime; // in minutes
  final int totalSessions;
  final double averageSessionDuration;
  final Map<String, int> subjectTimeDistribution;
  final Map<String, double> conceptMasteryRates;
  final List<DailyActivity> dailyActivities;
  final double overallProgress;
  final int streakDays;

  const LearningAnalytics({
    required this.userId,
    required this.startDate,
    required this.endDate,
    required this.totalStudyTime,
    required this.totalSessions,
    required this.averageSessionDuration,
    required this.subjectTimeDistribution,
    required this.conceptMasteryRates,
    required this.dailyActivities,
    required this.overallProgress,
    required this.streakDays,
  });

  /// Gets total study time in hours
  double get totalStudyHours => totalStudyTime / 60.0;

  /// Gets average daily study time in minutes
  double get averageDailyStudyTime {
    final days = endDate.difference(startDate).inDays + 1;
    return totalStudyTime / days;
  }

  @override
  String toString() {
    return 'LearningAnalytics(userId: $userId, totalTime: ${totalStudyHours.toStringAsFixed(1)}h, sessions: $totalSessions)';
  }
}

/// Represents daily learning activity
class DailyActivity {
  final DateTime date;
  final int minutesStudied;
  final int sessionsCompleted;
  final List<String> subjectsStudied;
  final double averageScore;

  const DailyActivity({
    required this.date,
    required this.minutesStudied,
    required this.sessionsCompleted,
    required this.subjectsStudied,
    required this.averageScore,
  });

  /// Gets study time in hours
  double get hoursStudied => minutesStudied / 60.0;

  @override
  String toString() {
    return 'DailyActivity(date: $date, minutes: $minutesStudied, sessions: $sessionsCompleted)';
  }
}

/// Represents a learning streak
class LearningStreak {
  final String userId;
  final int currentStreak;
  final int longestStreak;
  final DateTime lastStudyDate;
  final DateTime streakStartDate;
  final List<DateTime> studyDates;

  const LearningStreak({
    required this.userId,
    required this.currentStreak,
    required this.longestStreak,
    required this.lastStudyDate,
    required this.streakStartDate,
    required this.studyDates,
  });

  /// Checks if the streak is active (studied today or yesterday)
  bool get isActive {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final lastStudy = DateTime(
      lastStudyDate.year,
      lastStudyDate.month,
      lastStudyDate.day,
    );

    return lastStudy.isAtSameMomentAs(today) ||
        lastStudy.isAtSameMomentAs(yesterday);
  }

  /// Creates a copy of this learning streak with the given fields replaced
  LearningStreak copyWith({
    String? userId,
    int? currentStreak,
    int? longestStreak,
    DateTime? lastStudyDate,
    DateTime? streakStartDate,
    List<DateTime>? studyDates,
  }) {
    return LearningStreak(
      userId: userId ?? this.userId,
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      lastStudyDate: lastStudyDate ?? this.lastStudyDate,
      streakStartDate: streakStartDate ?? this.streakStartDate,
      studyDates: studyDates ?? this.studyDates,
    );
  }

  @override
  String toString() {
    return 'LearningStreak(current: $currentStreak, longest: $longestStreak, active: $isActive)';
  }
}

/// Represents a learning goal
class LearningGoal {
  final String id;
  final String userId;
  final String title;
  final String description;
  final String subject;
  final String? topic;
  final DateTime targetDate;
  final double targetProgress; // 0.0 to 1.0
  final double currentProgress; // 0.0 to 1.0
  final bool isCompleted;
  final DateTime createdAt;
  final DateTime? completedAt;
  final GoalType type;
  final Map<String, dynamic> metadata;

  const LearningGoal({
    required this.id,
    required this.userId,
    required this.title,
    required this.description,
    required this.subject,
    this.topic,
    required this.targetDate,
    required this.targetProgress,
    required this.currentProgress,
    required this.isCompleted,
    required this.createdAt,
    this.completedAt,
    required this.type,
    required this.metadata,
  });

  /// Gets the progress as a percentage
  double get progressPercentage => currentProgress * 100;

  /// Gets the target progress as a percentage
  double get targetPercentage => targetProgress * 100;

  /// Checks if the goal is overdue
  bool get isOverdue => !isCompleted && DateTime.now().isAfter(targetDate);

  /// Gets the time remaining to achieve the goal
  Duration get timeRemaining {
    final now = DateTime.now();
    if (now.isAfter(targetDate)) return Duration.zero;
    return targetDate.difference(now);
  }

  /// Creates a copy of this learning goal with the given fields replaced
  LearningGoal copyWith({
    String? id,
    String? userId,
    String? title,
    String? description,
    String? subject,
    String? topic,
    DateTime? targetDate,
    double? targetProgress,
    double? currentProgress,
    bool? isCompleted,
    DateTime? createdAt,
    DateTime? completedAt,
    GoalType? type,
    Map<String, dynamic>? metadata,
  }) {
    return LearningGoal(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      description: description ?? this.description,
      subject: subject ?? this.subject,
      topic: topic ?? this.topic,
      targetDate: targetDate ?? this.targetDate,
      targetProgress: targetProgress ?? this.targetProgress,
      currentProgress: currentProgress ?? this.currentProgress,
      isCompleted: isCompleted ?? this.isCompleted,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
      type: type ?? this.type,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'LearningGoal(title: $title, progress: ${progressPercentage.toStringAsFixed(1)}%, completed: $isCompleted)';
  }
}

/// Enum representing different types of learning goals
enum GoalType { masteryGoal, timeGoal, streakGoal, quizGoal, flashcardGoal }

/// Extension for goal type
extension GoalTypeExtension on GoalType {
  String get displayName {
    switch (this) {
      case GoalType.masteryGoal:
        return 'Mastery Goal';
      case GoalType.timeGoal:
        return 'Time Goal';
      case GoalType.streakGoal:
        return 'Streak Goal';
      case GoalType.quizGoal:
        return 'Quiz Goal';
      case GoalType.flashcardGoal:
        return 'Flashcard Goal';
    }
  }

  String get description {
    switch (this) {
      case GoalType.masteryGoal:
        return 'Master specific concepts or topics';
      case GoalType.timeGoal:
        return 'Study for a specific amount of time';
      case GoalType.streakGoal:
        return 'Maintain a study streak';
      case GoalType.quizGoal:
        return 'Complete quizzes with target scores';
      case GoalType.flashcardGoal:
        return 'Review flashcards regularly';
    }
  }
}

/// Represents a learning recommendation
class LearningRecommendation {
  final String id;
  final String title;
  final String description;
  final RecommendationType type;
  final int priority; // 1-5, 5 being highest
  final String subject;
  final String? topic;
  final Duration estimatedTime;
  final Map<String, dynamic> metadata;

  const LearningRecommendation({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.priority,
    required this.subject,
    this.topic,
    required this.estimatedTime,
    required this.metadata,
  });

  @override
  String toString() {
    return 'LearningRecommendation(title: $title, type: $type, priority: $priority)';
  }
}

/// Enum representing different types of learning recommendations
enum RecommendationType {
  reviewWeakConcepts,
  practiceFlashcards,
  takeQuiz,
  studyNewTopic,
  reviewMistakes,
  maintainStreak,
}
