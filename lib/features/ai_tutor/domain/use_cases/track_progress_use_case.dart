import 'package:dartz/dartz.dart';
import '../entities/learning_progress.dart';
import '../entities/learning_session.dart';
import '../entities/quiz.dart';
import '../repositories/learning_progress_repository.dart';
import '../../../../core/error/failures.dart';

/// Use case for tracking learning progress
class TrackProgressUseCase {
  final LearningProgressRepository _repository;

  TrackProgressUseCase(this._repository);

  /// Retrieves learning progress for a user
  Future<Either<Failure, LearningProgress?>> call(
    TrackProgressParams params,
  ) async {
    try {
      // Validate parameters
      final validationResult = ProgressValidator.validateParams(params);
      if (validationResult.isLeft()) {
        return validationResult.fold(
          (failure) => Left(failure),
          (_) => const Right(null), // This won't be reached
        );
      }

      // Get learning progress
      return await _repository.getLearningProgress(
        userId: params.userId,
        subject: params.subject,
        topic: params.topic,
      );
    } catch (e) {
      return Left(ServerFailure('Failed to track progress: ${e.toString()}'));
    }
  }

  /// Updates progress based on a completed learning session
  Future<Either<Failure, LearningProgress>> updateProgressFromSession({
    required LearningSession session,
  }) async {
    try {
      // Get current progress
      final progressResult = await _repository.getLearningProgress(
        userId: session.userId,
        subject: session.subject,
        topic: session.topic,
      );

      return progressResult.fold(
        (failure) => Left(failure),
        (currentProgress) async {
          // Calculate new progress
          final updatedProgress = _calculateProgressFromSession(
            session: session,
            currentProgress: currentProgress,
          );

          // Save updated progress
          final saveResult = await _repository.saveLearningProgress(updatedProgress);
          
          return saveResult.fold(
            (failure) => Left(failure),
            (_) => Right(updatedProgress),
          );
        },
      );
    } catch (e) {
      return Left(ServerFailure('Failed to update progress from session: ${e.toString()}'));
    }
  }

  /// Updates progress based on quiz results
  Future<Either<Failure, LearningProgress>> updateProgressFromQuiz({
    required QuizResult quizResult,
    required String subject,
    required String topic,
  }) async {
    try {
      // Get current progress
      final progressResult = await _repository.getLearningProgress(
        userId: quizResult.userId,
        subject: subject,
        topic: topic,
      );

      return progressResult.fold(
        (failure) => Left(failure),
        (currentProgress) async {
          // Calculate new progress
          final updatedProgress = _calculateProgressFromQuiz(
            quizResult: quizResult,
            subject: subject,
            topic: topic,
            currentProgress: currentProgress,
          );

          // Save updated progress
          final saveResult = await _repository.saveLearningProgress(updatedProgress);
          
          return saveResult.fold(
            (failure) => Left(failure),
            (_) => Right(updatedProgress),
          );
        },
      );
    } catch (e) {
      return Left(ServerFailure('Failed to update progress from quiz: ${e.toString()}'));
    }
  }

  /// Gets comprehensive learning analytics
  Future<Either<Failure, LearningAnalytics>> getAnalytics({
    required String userId,
    String? subject,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      return await _repository.getLearningAnalytics(
        userId: userId,
        subject: subject,
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      return Left(ServerFailure('Failed to get analytics: ${e.toString()}'));
    }
  }

  /// Updates daily learning activity
  Future<Either<Failure, void>> recordDailyActivity({
    required String userId,
    required DateTime date,
    required int minutesStudied,
  }) async {
    try {
      return await _repository.updateDailyActivity(
        userId: userId,
        date: date,
        minutesStudied: minutesStudied,
      );
    } catch (e) {
      return Left(ServerFailure('Failed to record daily activity: ${e.toString()}'));
    }
  }

  /// Calculates progress from a learning session
  LearningProgress _calculateProgressFromSession({
    required LearningSession session,
    LearningProgress? currentProgress,
  }) {
    final now = DateTime.now();
    
    // Create new progress if none exists
    if (currentProgress == null) {
      return LearningProgress(
        id: 'progress_${session.userId}_${session.subject}_${session.topic}',
        userId: session.userId,
        subject: session.subject,
        topic: session.topic,
        overallProgress: 0.1, // Initial progress
        conceptProgress: {
          for (final concept in session.conceptsCovered) concept: 0.2
        },
        masteredConcepts: [],
        strugglingConcepts: [],
        lastUpdated: now,
        stats: LearningStats(
          totalStudyTime: session.duration?.inMinutes ?? 0,
          sessionsCompleted: 1,
          flashcardsReviewed: 0,
          quizzesCompleted: 0,
          averageQuizScore: 0.0,
          streakDays: 1,
          lastStudyDate: now,
          weeklyActivity: {
            _getDayKey(now): session.duration?.inMinutes ?? 0,
          },
        ),
        metadata: {},
      );
    }

    // Update existing progress
    final updatedConceptProgress = Map<String, double>.from(currentProgress.conceptProgress);
    final sessionDuration = session.duration?.inMinutes ?? 0;
    
    // Update concept progress based on session
    for (final concept in session.conceptsCovered) {
      final currentConceptProgress = updatedConceptProgress[concept] ?? 0.0;
      final progressIncrease = (session.comprehensionScore / 100) * 0.1; // 10% max increase
      updatedConceptProgress[concept] = (currentConceptProgress + progressIncrease).clamp(0.0, 1.0);
    }

    // Calculate overall progress
    final overallProgress = updatedConceptProgress.values.isEmpty 
        ? 0.0 
        : updatedConceptProgress.values.reduce((a, b) => a + b) / updatedConceptProgress.length;

    // Update mastered and struggling concepts
    final masteredConcepts = List<String>.from(currentProgress.masteredConcepts);
    final strugglingConcepts = List<String>.from(currentProgress.strugglingConcepts);

    for (final concept in session.conceptsCovered) {
      final conceptProgress = updatedConceptProgress[concept] ?? 0.0;
      
      if (conceptProgress >= 0.9 && !masteredConcepts.contains(concept)) {
        masteredConcepts.add(concept);
        strugglingConcepts.remove(concept);
      } else if (conceptProgress < 0.3 && !strugglingConcepts.contains(concept)) {
        strugglingConcepts.add(concept);
      }
    }

    // Update stats
    final updatedStats = currentProgress.stats.copyWith(
      totalStudyTime: currentProgress.stats.totalStudyTime + sessionDuration,
      sessionsCompleted: currentProgress.stats.sessionsCompleted + 1,
      lastStudyDate: now,
      weeklyActivity: {
        ...currentProgress.stats.weeklyActivity,
        _getDayKey(now): (currentProgress.stats.weeklyActivity[_getDayKey(now)] ?? 0) + sessionDuration,
      },
    );

    return currentProgress.copyWith(
      overallProgress: overallProgress,
      conceptProgress: updatedConceptProgress,
      masteredConcepts: masteredConcepts,
      strugglingConcepts: strugglingConcepts,
      lastUpdated: now,
      stats: updatedStats,
    );
  }

  /// Calculates progress from quiz results
  LearningProgress _calculateProgressFromQuiz({
    required QuizResult quizResult,
    required String subject,
    required String topic,
    LearningProgress? currentProgress,
  }) {
    final now = DateTime.now();
    
    // Extract concepts from quiz answers
    final concepts = quizResult.answers.map((answer) => answer.concept).toSet().toList();
    
    // Create new progress if none exists
    if (currentProgress == null) {
      return LearningProgress(
        id: 'progress_${quizResult.userId}_${subject}_$topic',
        userId: quizResult.userId,
        subject: subject,
        topic: topic,
        overallProgress: quizResult.percentage / 100,
        conceptProgress: {
          for (final concept in concepts) 
            concept: quizResult.answers
                .where((answer) => answer.concept == concept)
                .map((answer) => answer.isCorrect ? 1.0 : 0.0)
                .reduce((a, b) => a + b) / 
                quizResult.answers.where((answer) => answer.concept == concept).length
        },
        masteredConcepts: [],
        strugglingConcepts: [],
        lastUpdated: now,
        stats: LearningStats(
          totalStudyTime: quizResult.timeSpent.inMinutes,
          sessionsCompleted: 0,
          flashcardsReviewed: 0,
          quizzesCompleted: 1,
          averageQuizScore: quizResult.percentage,
          streakDays: 1,
          lastStudyDate: now,
          weeklyActivity: {
            _getDayKey(now): quizResult.timeSpent.inMinutes,
          },
        ),
        metadata: {},
      );
    }

    // Update existing progress
    final updatedConceptProgress = Map<String, double>.from(currentProgress.conceptProgress);
    
    // Update concept progress based on quiz performance
    for (final concept in concepts) {
      final conceptAnswers = quizResult.answers.where((answer) => answer.concept == concept);
      final conceptScore = conceptAnswers.map((answer) => answer.isCorrect ? 1.0 : 0.0)
          .reduce((a, b) => a + b) / conceptAnswers.length;
      
      final currentConceptProgress = updatedConceptProgress[concept] ?? 0.0;
      // Weighted average: 70% current progress, 30% new quiz score
      updatedConceptProgress[concept] = (currentConceptProgress * 0.7 + conceptScore * 0.3).clamp(0.0, 1.0);
    }

    // Calculate overall progress
    final overallProgress = updatedConceptProgress.values.isEmpty 
        ? 0.0 
        : updatedConceptProgress.values.reduce((a, b) => a + b) / updatedConceptProgress.length;

    // Update stats
    final totalQuizzes = currentProgress.stats.quizzesCompleted + 1;
    final newAverageScore = (currentProgress.stats.averageQuizScore * currentProgress.stats.quizzesCompleted + 
        quizResult.percentage) / totalQuizzes;

    final updatedStats = currentProgress.stats.copyWith(
      totalStudyTime: currentProgress.stats.totalStudyTime + quizResult.timeSpent.inMinutes,
      quizzesCompleted: totalQuizzes,
      averageQuizScore: newAverageScore,
      lastStudyDate: now,
      weeklyActivity: {
        ...currentProgress.stats.weeklyActivity,
        _getDayKey(now): (currentProgress.stats.weeklyActivity[_getDayKey(now)] ?? 0) + 
            quizResult.timeSpent.inMinutes,
      },
    );

    return currentProgress.copyWith(
      overallProgress: overallProgress,
      conceptProgress: updatedConceptProgress,
      lastUpdated: now,
      stats: updatedStats,
    );
  }

  /// Gets a day key for weekly activity tracking
  String _getDayKey(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}

/// Parameters for tracking progress
class TrackProgressParams {
  final String userId;
  final String? subject;
  final String? topic;

  const TrackProgressParams({
    required this.userId,
    this.subject,
    this.topic,
  });

  @override
  String toString() {
    return 'TrackProgressParams(userId: $userId, subject: $subject, topic: $topic)';
  }
}

/// Validation helper for progress parameters
class ProgressValidator {
  /// Validates the parameters for tracking progress
  static Either<Failure, void> validateParams(TrackProgressParams params) {
    // Validate user ID
    if (params.userId.trim().isEmpty) {
      return const Left(ValidationFailure('User ID cannot be empty'));
    }

    // Validate subject if provided
    if (params.subject != null && params.subject!.trim().isEmpty) {
      return const Left(ValidationFailure('Subject cannot be empty if provided'));
    }

    // Validate topic if provided
    if (params.topic != null && params.topic!.trim().isEmpty) {
      return const Left(ValidationFailure('Topic cannot be empty if provided'));
    }

    return const Right(null);
  }
}
