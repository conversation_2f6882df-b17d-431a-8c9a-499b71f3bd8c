import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import '../../../../models/usage.dart';
import '../../../../widgets/enhanced_app_bar.dart';
import '../bloc/ai_tutor_bloc.dart';
import '../widgets/learning_plan_widget.dart';
import '../widgets/flashcard_widget.dart';
import '../widgets/quiz_widget.dart';
import '../widgets/progress_chart_widget.dart';

/// Main dashboard page for the AI Tutor feature
class AITutorDashboardPage extends StatefulWidget {
  final String title;
  final Usage type;

  const AITutorDashboardPage({
    Key? key,
    required this.title,
    required this.type,
  }) : super(key: key);

  @override
  State<AITutorDashboardPage> createState() => _AITutorDashboardPageState();
}

class _AITutorDashboardPageState extends State<AITutorDashboardPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<AITutorBloc>(),
      child: Scaffold(
        appBar: EnhancedAppBar(
          title: widget.title,
          showBackButton: true,
          actions: [
            IconButton(
              icon: const Icon(Icons.analytics),
              onPressed: () => _navigateToAnalytics(),
              tooltip: 'Learning Analytics',
            ),
            IconButton(
              icon: const Icon(Icons.settings),
              onPressed: () => _navigateToSettings(),
              tooltip: 'Settings',
            ),
          ],
        ),
        body: Column(
          children: [
            _buildTabBar(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildLearningPlanTab(),
                  _buildFlashcardsTab(),
                  _buildQuizTab(),
                  _buildProgressTab(),
                ],
              ),
            ),
          ],
        ),
        floatingActionButton: _buildFloatingActionButton(),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(icon: Icon(Icons.map), text: 'Learning Plan'),
          Tab(icon: Icon(Icons.style), text: 'Flashcards'),
          Tab(icon: Icon(Icons.quiz), text: 'Quiz'),
          Tab(icon: Icon(Icons.trending_up), text: 'Progress'),
        ],
        labelColor: Theme.of(context).colorScheme.primary,
        unselectedLabelColor: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
        indicatorColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  Widget _buildLearningPlanTab() {
    return BlocBuilder<AITutorBloc, AITutorState>(
      builder: (context, state) {
        if (state is AITutorInitial) {
          return _buildLearningPlanSetup();
        } else if (state is AITutorLoading) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Generating your personalized learning plan...'),
              ],
            ),
          );
        } else if (state is LearningPlanGenerated) {
          return LearningPlanWidget(learningPlan: state.learningPlan);
        } else if (state is AITutorError) {
          return _buildErrorWidget(state.message);
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildLearningPlanSetup() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeCard(),
          const SizedBox(height: 24),
          _buildQuickStartCard(),
          const SizedBox(height: 24),
          _buildFeaturesOverview(),
        ],
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.school,
                  size: 32,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Welcome to AI Tutor',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Your personalized learning companion powered by AI. Create custom learning plans, practice with flashcards, take adaptive quizzes, and track your progress.',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStartCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Start',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildSubjectSelector(),
            const SizedBox(height: 16),
            _buildLevelSelector(),
            const SizedBox(height: 16),
            _buildGoalsInput(),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _generateLearningPlan,
                icon: const Icon(Icons.auto_awesome),
                label: const Text('Generate Learning Plan'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturesOverview() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Features',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildFeatureItem(
              icon: Icons.map,
              title: 'Personalized Learning Plans',
              description: 'AI-generated study plans tailored to your goals and learning style.',
            ),
            _buildFeatureItem(
              icon: Icons.style,
              title: 'Smart Flashcards',
              description: 'Spaced repetition system that adapts to your memory patterns.',
            ),
            _buildFeatureItem(
              icon: Icons.quiz,
              title: 'Adaptive Quizzes',
              description: 'Dynamic quizzes that adjust difficulty based on your performance.',
            ),
            _buildFeatureItem(
              icon: Icons.trending_up,
              title: 'Progress Tracking',
              description: 'Detailed analytics to monitor your learning journey.',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: Theme.of(context).colorScheme.primary,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // TODO: Implement subject selector with dropdown
  Widget _buildSubjectSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Subject',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Container(
          height: 60,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Text('Subject selector - TODO: Implement dropdown'),
          ),
        ),
      ],
    );
  }

  // TODO: Implement level selector with radio buttons
  Widget _buildLevelSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Current Level',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Container(
          height: 80,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Text('Level selector - TODO: Implement radio buttons'),
          ),
        ),
      ],
    );
  }

  // TODO: Implement goals input with chips
  Widget _buildGoalsInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Learning Goals',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Container(
          height: 100,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Text('Goals input - TODO: Implement chips input'),
          ),
        ),
      ],
    );
  }

  Widget _buildFlashcardsTab() {
    return const Center(
      child: Text('Flashcards - TODO: Implement flashcard study interface'),
    );
  }

  Widget _buildQuizTab() {
    return const Center(
      child: Text('Quiz - TODO: Implement quiz interface'),
    );
  }

  Widget _buildProgressTab() {
    return const Center(
      child: Text('Progress - TODO: Implement progress analytics'),
    );
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Oops! Something went wrong',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _generateLearningPlan(),
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: () => _showQuickActions(),
      icon: const Icon(Icons.add),
      label: const Text('Quick Action'),
    );
  }

  void _generateLearningPlan() {
    // TODO: Collect form data and dispatch event
    context.read<AITutorBloc>().add(
      GenerateLearningPlanEvent(
        subject: 'Mathematics', // From form
        currentLevel: 'Beginner', // From form
        learningGoals: ['Algebra'], // From form
        preferences: {}, // From form
      ),
    );
  }

  void _navigateToAnalytics() {
    // TODO: Navigate to analytics page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Analytics page - TODO: Implement')),
    );
  }

  void _navigateToSettings() {
    // TODO: Navigate to settings page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Settings page - TODO: Implement')),
    );
  }

  void _showQuickActions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.style),
              title: const Text('Create Flashcards'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to flashcard creation
              },
            ),
            ListTile(
              leading: const Icon(Icons.quiz),
              title: const Text('Take Quiz'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to quiz
              },
            ),
            ListTile(
              leading: const Icon(Icons.school),
              title: const Text('Study Session'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Start study session
              },
            ),
          ],
        ),
      ),
    );
  }
}
