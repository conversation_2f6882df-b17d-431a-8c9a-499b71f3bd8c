import 'package:flutter/material.dart';
import '../../domain/entities/flashcard.dart';

/// Widget for displaying and interacting with flashcards
class FlashcardWidget extends StatefulWidget {
  final Flashcard flashcard;
  final VoidCallback? onNext;
  final Function(FlashcardResponse)? onResponse;
  final bool showAnswer;

  const FlashcardWidget({
    Key? key,
    required this.flashcard,
    this.onNext,
    this.onResponse,
    this.showAnswer = false,
  }) : super(key: key);

  @override
  State<FlashcardWidget> createState() => _FlashcardWidgetState();
}

class _FlashcardWidgetState extends State<FlashcardWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _flipAnimation;
  bool _isFlipped = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _flipAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (widget.showAnswer) {
      _isFlipped = true;
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _flipCard() {
    if (!_isFlipped) {
      _animationController.forward();
      setState(() {
        _isFlipped = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildFlashcard(context),
        if (_isFlipped) ...[
          const SizedBox(height: 24),
          _buildResponseButtons(context),
        ],
      ],
    );
  }

  Widget _buildFlashcard(BuildContext context) {
    return GestureDetector(
      onTap: _flipCard,
      child: AnimatedBuilder(
        animation: _flipAnimation,
        builder: (context, child) {
          final isShowingFront = _flipAnimation.value < 0.5;
          return Transform(
            alignment: Alignment.center,
            transform: Matrix4.identity()
              ..setEntry(3, 2, 0.001)
              ..rotateY(_flipAnimation.value * 3.14159),
            child: Card(
              elevation: 8,
              child: Container(
                width: double.infinity,
                height: 300,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Theme.of(context).colorScheme.primaryContainer,
                      Theme.of(context).colorScheme.primaryContainer.withOpacity(0.8),
                    ],
                  ),
                ),
                child: isShowingFront
                    ? _buildCardFront(context)
                    : Transform(
                        alignment: Alignment.center,
                        transform: Matrix4.identity()..rotateY(3.14159),
                        child: _buildCardBack(context),
                      ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCardFront(BuildContext context) {
    return Column(
      children: [
        _buildCardHeader(context),
        const SizedBox(height: 24),
        Expanded(
          child: Center(
            child: Text(
              widget.flashcard.front,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.onPrimaryContainer,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        if (!_isFlipped) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface.withOpacity(0.8),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.touch_app,
                  size: 16,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                const SizedBox(width: 8),
                Text(
                  'Tap to reveal answer',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildCardBack(BuildContext context) {
    return Column(
      children: [
        _buildCardHeader(context),
        const SizedBox(height: 24),
        Expanded(
          child: SingleChildScrollView(
            child: Text(
              widget.flashcard.back,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onPrimaryContainer,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCardHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Color(widget.flashcard.difficulty.colorValue).withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Color(widget.flashcard.difficulty.colorValue),
              width: 1,
            ),
          ),
          child: Text(
            widget.flashcard.difficulty.displayName,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Color(widget.flashcard.difficulty.colorValue),
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        const Spacer(),
        if (widget.flashcard.reviewCount > 0) ...[
          Icon(
            Icons.refresh,
            size: 16,
            color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.7),
          ),
          const SizedBox(width: 4),
          Text(
            '${widget.flashcard.reviewCount}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.7),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildResponseButtons(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'How well did you know this?',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildResponseButton(
                    context,
                    FlashcardResponse.hard,
                    'Hard',
                    'I didn\'t know this',
                    Colors.red,
                    Icons.close,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildResponseButton(
                    context,
                    FlashcardResponse.good,
                    'Good',
                    'I knew this with effort',
                    Colors.orange,
                    Icons.check,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildResponseButton(
                    context,
                    FlashcardResponse.easy,
                    'Easy',
                    'I knew this well',
                    Colors.green,
                    Icons.done_all,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResponseButton(
    BuildContext context,
    FlashcardResponse response,
    String title,
    String subtitle,
    Color color,
    IconData icon,
  ) {
    return ElevatedButton(
      onPressed: () => widget.onResponse?.call(response),
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withOpacity(0.1),
        foregroundColor: color,
        elevation: 0,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: color.withOpacity(0.3)),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
